import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:mental_health/core/services/firebase_data_seeder.dart';
import 'package:mental_health/core/theme.dart';
import 'package:mental_health/features/meditation/presentation/bloc/daily_quote/daily_quote_bloc.dart';
import 'package:mental_health/features/meditation/presentation/bloc/daily_quote/daily_quote_event.dart';
import 'package:mental_health/features/meditation/presentation/bloc/daily_quote/daily_quote_state.dart';
import 'package:mental_health/features/meditation/presentation/bloc/mood_messenge/mood_messenge_bloc.dart';
import 'package:mental_health/features/meditation/presentation/bloc/mood_messenge/mood_messenge_event.dart';
import 'package:mental_health/features/meditation/presentation/bloc/mood_messenge/mood_messenge_state.dart';

import 'package:mental_health/features/meditation/presentation/widgets/feeling_button.dart';
import 'package:mental_health/features/meditation/presentation/widgets/meditation/emotion_core.dart';
import 'package:mental_health/features/meditation/presentation/widgets/mini_task_card.dart';

class MeditationScreen extends StatefulWidget {
  const MeditationScreen({Key? key}) : super(key: key);

  @override
  State<MeditationScreen> createState() => _MeditationScreenState();
}

class _MeditationScreenState extends State<MeditationScreen> {
  // Track completion status for each task
  bool _morningCompleted = false;
  bool _noonCompleted = false;
  bool _eveningCompleted = false;

  void _showTaskCompletionFeedback(String taskName, bool isCompleted) {
    final message = isCompleted
        ? 'Tuyệt vời! Bạn đã hoàn thành nhiệm vụ $taskName 🎉'
        : 'Nhiệm vụ $taskName đã được đánh dấu chưa hoàn thành';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor:
            isCompleted ? Colors.green.shade600 : Colors.orange.shade600,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showTaskDetails(String taskName, String description) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              _getTaskIcon(taskName),
              color: _getTaskIconColor(taskName),
              size: 28,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                taskName,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Chi tiết nhiệm vụ:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                description,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      height: 1.5,
                    ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  _getTaskTimeRange(taskName),
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Có thể thêm logic để đánh dấu hoàn thành từ đây
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: _getTaskIconColor(taskName),
              foregroundColor: DefaultColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Bắt đầu'),
          ),
        ],
      ),
    );
  }

  IconData _getTaskIcon(String taskName) {
    switch (taskName.toLowerCase()) {
      case 'buổi sáng':
        return Icons.wb_sunny;
      case 'buổi trưa':
        return Icons.wb_sunny_outlined;
      case 'buổi tối':
        return Icons.nightlight_round;
      default:
        return Icons.schedule;
    }
  }

  Color _getTaskIconColor(String taskName) {
    switch (taskName.toLowerCase()) {
      case 'buổi sáng':
        return Colors.orange.shade600;
      case 'buổi trưa':
        return Colors.amber.shade700;
      case 'buổi tối':
        return Colors.indigo.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  String _getTaskTimeRange(String taskName) {
    switch (taskName.toLowerCase()) {
      case 'buổi sáng':
        return 'Thời gian khuyến nghị: 6:00 - 12:00';
      case 'buổi trưa':
        return 'Thời gian khuyến nghị: 12:00 - 18:00';
      case 'buổi tối':
        return 'Thời gian khuyến nghị: 18:00 - 22:00';
      default:
        return 'Cả ngày';
    }
  }

  Widget _buildModernTaskCard(
    String title,
    String description,
    IconData icon,
    Color primaryColor,
    Color accentColor,
    bool isCompleted,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.02),
          ],
        ),
        border: Border.all(
          color: isCompleted
              ? accentColor.withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.15),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isCompleted
                ? accentColor.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.04),
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [
                  primaryColor.withValues(alpha: isCompleted ? 0.8 : 0.2),
                  accentColor.withValues(alpha: isCompleted ? 0.6 : 0.1),
                ],
              ),
            ),
            child: Icon(
              isCompleted ? Icons.check_rounded : icon,
              color: isCompleted ? Colors.white : accentColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextPresets.bodyLarge.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                    decoration: isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextPresets.bodySmall.copyWith(
                    color: Colors.black.withValues(alpha: 0.7),
                    decoration: isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isCompleted
                    ? accentColor
                    : Colors.black.withValues(alpha: 0.3),
                width: 2,
              ),
              color: isCompleted ? accentColor : Colors.transparent,
            ),
            child: isCompleted
                ? const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 18,
                  )
                : null,
          ),
        ],
      ),
    );
  }

  Widget _buildModernFeatureCard(
    String title,
    IconData icon,
    Color primaryColor,
    Color accentColor,
  ) {
    return Column(
      children: [
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                primaryColor.withValues(alpha: 0.2),
                accentColor.withValues(alpha: 0.1),
              ],
            ),
            border: Border.all(
              color: accentColor.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: accentColor.withValues(alpha: 0.15),
                blurRadius: 12,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            icon,
            color: accentColor,
            size: 28,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: TextPresets.labelSmall.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent, // Để nền trong suốt
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        leadingWidth: 100, // AppBar trong suốt
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Row(
              spacing: 12,
              children: [
                // Modern Stats Container with Glassmorphism
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withValues(alpha: 0.25),
                        Colors.white.withValues(alpha: 0.1),
                      ],
                    ),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.2),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Streak Counter
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          gradient: LinearGradient(
                            colors: [
                              DefaultColors.endorphin.withValues(alpha: 0.8),
                              DefaultColors.endorphinNeon
                                  .withValues(alpha: 0.6),
                            ],
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: Image.asset('assets/icons/flame.png'),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '100',
                              style: TextPresets.labelLarge.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w800,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Coins Counter
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          gradient: LinearGradient(
                            colors: [
                              DefaultColors.dopamine.withValues(alpha: 0.8),
                              DefaultColors.dopamineNeon.withValues(alpha: 0.6),
                            ],
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: Image.asset('assets/icons/coin.png'),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '100K',
                              style: TextPresets.labelLarge.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w800,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 4),
                      // Add Button
                      Container(
                        width: 28,
                        height: 28,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              DefaultColors.serotonin.withValues(alpha: 0.8),
                              DefaultColors.serotoninNeon
                                  .withValues(alpha: 0.6),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: DefaultColors.serotonin
                                  .withValues(alpha: 0.3),
                              blurRadius: 8,
                              spreadRadius: 0,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ),
                // Modern Profile Avatar
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        DefaultColors.oxytocin.withValues(alpha: 0.3),
                        DefaultColors.oxytocinNeon.withValues(alpha: 0.1),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: DefaultColors.oxytocin.withValues(alpha: 0.2),
                        blurRadius: 12,
                        spreadRadius: 0,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const CircleAvatar(
                    radius: 20,
                    backgroundImage: AssetImage('assets/images/profile.png'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Container(
        color: Colors.transparent,
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            spacing: 16,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Modern Welcome Section
              Column(
                spacing: 16,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Image.asset(
                        'assets/images/rose.png',
                        width: 35,
                        height: 35,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Xin chào \$user,',
                                style: TextPresets.subTitle),
                            Text('Chúc bạn một ngày mới vui vẻ!',
                                style: TextPresets.title),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: DefaultColors.serotoninNeon.withValues(alpha: 0.3),
                      border: Border.all(
                        color: Colors.black.withValues(alpha: 0.1),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                DefaultColors.white.withValues(alpha: 0.3),
                                DefaultColors.serotonin.withValues(alpha: 0.3),
                              ],
                            ),
                          ),
                          child: Icon(
                            Icons.auto_awesome,
                            color: Colors.black,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Hôm nay bạn cảm thấy thế nào?',
                                style: TextPresets.bodyMedium.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                'Hãy bắt đầu hành trình tâm linh của bạn',
                                style: TextPresets.bodySmall.copyWith(),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.black,
                            size: 20,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              EmotionCore(),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  borderRadius: DfBRadius.defaultRadius,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withValues(alpha: 0.12),
                      Colors.white.withValues(alpha: 0.04),
                    ],
                  ),
                  border: Border.all(
                    color: Colors.black.withValues(alpha: 0.18),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.06),
                      blurRadius: 16,
                      spreadRadius: 0,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: LinearGradient(
                              colors: [
                                DefaultColors.endorphin.withValues(alpha: 0.2),
                                DefaultColors.endorphinNeon
                                    .withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                          child: const Icon(
                            Icons.task_alt_rounded,
                            color: DefaultColors.endorphinNeon,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text('Nhiệm vụ hôm nay', style: TextPresets.title),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            gradient: LinearGradient(
                              colors: [
                                DefaultColors.serotonin.withValues(alpha: 0.2),
                                DefaultColors.serotoninNeon
                                    .withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                          child: Text('3/6', style: TextPresets.label),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildModernTaskCard(
                      'Thực hành biết ơn',
                      'Viết ra 3 điều biết ơn trong ngày hôm nay',
                      Icons.favorite_rounded,
                      DefaultColors.oxytocin,
                      DefaultColors.oxytocinNeon,
                      true,
                    ),
                    const SizedBox(height: 12),
                    _buildModernTaskCard(
                      'Thiền định 10 phút',
                      'Dành thời gian tĩnh lặng để kết nối với bản thân',
                      Icons.self_improvement_rounded,
                      DefaultColors.dopamine,
                      DefaultColors.dopamineNeon,
                      false,
                    ),
                    const SizedBox(height: 12),
                    _buildModernTaskCard(
                      'Vận động nhẹ nhàng',
                      'Yoga hoặc đi bộ 15 phút để thư giãn cơ thể',
                      Icons.directions_walk_rounded,
                      DefaultColors.endorphin,
                      DefaultColors.endorphinNeon,
                      false,
                    ),
                  ],
                ),
              ),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                margin: const EdgeInsets.only(bottom: 24),
                decoration: BoxDecoration(
                  borderRadius: DfBRadius.defaultRadius,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withValues(alpha: 0.12),
                      Colors.white.withValues(alpha: 0.04),
                    ],
                  ),
                  border: Border.all(
                    color: Colors.black.withValues(alpha: 0.18),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.06),
                      blurRadius: 16,
                      spreadRadius: 0,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            borderRadius: DfBRadius.defaultRadius,
                            gradient: LinearGradient(
                              colors: [
                                DefaultColors.serotonin.withValues(alpha: 0.2),
                                DefaultColors.serotoninNeon
                                    .withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                          child: const Icon(
                            Icons.explore_rounded,
                            color: DefaultColors.serotoninNeon,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text('Khám phá thêm', style: TextPresets.title),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildModernFeatureCard(
                          'Nhật ký',
                          Icons.book_rounded,
                          DefaultColors.dopamine,
                          DefaultColors.dopamineNeon,
                        ),
                        _buildModernFeatureCard(
                          'Uống nước',
                          Icons.analytics_rounded,
                          DefaultColors.endorphin,
                          DefaultColors.endorphinNeon,
                        ),
                        _buildModernFeatureCard(
                          'Nhắc nhở',
                          Icons.people_rounded,
                          DefaultColors.oxytocin,
                          DefaultColors.oxytocinNeon,
                        ),
                        _buildModernFeatureCard(
                          'Cài đặt',
                          Icons.settings_rounded,
                          DefaultColors.serotonin,
                          DefaultColors.serotoninNeon,
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

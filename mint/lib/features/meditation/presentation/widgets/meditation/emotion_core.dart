import 'package:flutter/material.dart';
import 'package:mental_health/core/theme.dart';
import 'package:mental_health/features/meditation/presentation/widgets/feeling_button.dart';

class EmotionCore extends StatelessWidget {
  const EmotionCore({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Container(
        width: double.infinity,
        child: Container(
          width: double.infinity,
          margin: const EdgeInsets.only(top: 20),
          padding: const EdgeInsets.only(top: 40, bottom: 20),
          decoration: BoxDecoration(
            color: DefaultColors.white,
            borderRadius: BorderRadius.only(
              // topRight: Radius.circular(8),
              bottomLeft: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
            border: Border.all(
              color: DefaultColors.serotonin,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              FeelingButton(
                feeling: '<PERSON><PERSON><PERSON> tiêu',
                process: 0.8,
                color: DefaultColors.dopamine,
                icon: 'assets/icons/target.svg',
                colorLight: DefaultColors.dopamineNeon,
              ),
              FeelingButton(
                feeling: 'Vận động',
                process: 0.6,
                color: DefaultColors.endorphin,
                icon: 'assets/icons/energy.svg',
                colorLight: DefaultColors.endorphinNeon,
              ),
              FeelingButton(
                feeling: 'Gắn kết',
                process: 0.3,
                color: DefaultColors.oxytocin,
                icon: 'assets/icons/heart.svg',
                colorLight: DefaultColors.oxytocinNeon,
              ),
              FeelingButton(
                feeling: 'Tự tin',
                process: 0.9,
                color: DefaultColors.serotonin,
                icon: 'assets/icons/galaxy-star.svg',
                colorLight: DefaultColors.serotoninNeon,
              ),
            ],
          ),
        ),
      ),
      Container(
          height: 28,
          margin: EdgeInsets.only(top: 6),
          padding: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
            color: Color.alphaBlend(
              DefaultColors.serotoninNeon.withOpacity(0.3),
              Colors.white,
            ),
            border: const Border(
              top: BorderSide(color: DefaultColors.serotonin, width: 1),
              left: BorderSide(color: DefaultColors.serotonin, width: 1),
              right: BorderSide(color: DefaultColors.serotonin, width: 1),
              bottom: BorderSide(color: DefaultColors.serotonin, width: 1),
            ),
          ),
          child: Text(
            'Lõi cảm xúc',
            style: TextPresets.bodyMedium.copyWith(
              fontWeight: FontWeight.w800,
            ),
          )),
      Positioned(
        top: 0,
        right: 10,
        child: Text('Xem lịch sử ...',
            style: TextPresets.body.copyWith(
              color: DefaultColors.serotonin,
            )),
      )
    ]);
  }
}

import 'package:flutter/material.dart';
import 'package:mental_health/core/theme.dart';
import 'package:mental_health/features/meditation/presentation/widgets/meditation/frame.dart';

class EmotionCore extends StatelessWidget {
  const EmotionCore({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Container(
        width: double.infinity,
        child: const Frame(),
      ),
      Container(
          height: 28,
          margin: const EdgeInsets.only(top: 6),
          padding: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
            color: Color.alphaBlend(
              DefaultColors.serotoninNeon.withValues(alpha: 0.3),
              Colors.white,
            ),
            border: const Border(
              top: BorderSide(color: DefaultColors.serotonin, width: 1),
              left: BorderSide(color: DefaultColors.serotonin, width: 1),
              right: BorderSide(color: DefaultColors.serotonin, width: 1),
              bottom: BorderSide(color: DefaultColors.serotonin, width: 1),
            ),
          ),
          child: Text(
            'Lõi cảm xúc',
            style: TextPresets.bodyMedium.copyWith(
              fontWeight: FontWeight.w800,
            ),
          )),
      Positioned(
        top: 0,
        right: 10,
        child: Text('Xem lịch sử ...',
            style: TextPresets.body.copyWith(
              color: DefaultColors.serotonin,
            )),
      )
    ]);
  }
}
